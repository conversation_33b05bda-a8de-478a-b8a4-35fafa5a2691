
INSERT INTO grammar_points (
    id, lesson_id, sequence_order, concept_english, concept_target_language,
    explanation_english, examples, difficulty_level, is_active
) VALUES (
            gen_random_uuid(),
            'ca75e761-0d7f-56c1-a372-eec78d50ef98',
            1,
            'Saying Hello',
            'வணக்கம் சொல்வது',
            'Say வணக்கம் anytime you meet someone.',
            '[{"example_english": "Hello", "example_tamil": "\u0bb5\u0ba3\u0b95\u0bcd\u0b95\u0bae\u0bcd", "example_romanized": "vanakkam"}, {"example_english": "Good morning", "example_tamil": "\u0b95\u0bbe\u0bb2\u0bc8 \u0bb5\u0ba3\u0b95\u0bcd\u0b95\u0bae\u0bcd", "example_romanized": "kaalai vanakkam"}]'::jsonb,
            'A1',
            true
        ), (
            gen_random_uuid(),
            'ca75e761-0d7f-56c1-a372-eec78d50ef98',
            2,
            'Saying "I am [name]"',
            'நான் [பெயர்]',
            'To say your name, say ''நான்'' then your name.',
            '[{"example_english": "I am Priya", "example_tamil": "\u0ba8\u0bbe\u0ba9\u0bcd \u0baa\u0bbf\u0bb0\u0bbf\u0baf\u0bbe", "example_romanized": "naan Priya"}, {"example_english": "I am Arjun", "example_tamil": "\u0ba8\u0bbe\u0ba9\u0bcd \u0b85\u0bb0\u0bcd\u0b9c\u0bc1\u0ba9\u0bcd", "example_romanized": "naan Arjun"}]'::jsonb,
            'A1',
            true
        ), (
            gen_random_uuid(),
            'ca75e761-0d7f-56c1-a372-eec78d50ef98',
            3,
            'Asking someone''s name',
            'உங்க பேர் என்ன?',
            'To ask someone''s name, say ''உங்க பேர் என்ன?'' ',
            '[{"example_english": "What is your name?", "example_tamil": "\u0b89\u0b99\u0bcd\u0b95 \u0baa\u0bc7\u0bb0\u0bcd \u0b8e\u0ba9\u0bcd\u0ba9?", "example_romanized": "unga paer enna?"}, {"example_english": "What is your name (slightly more polite)?", "example_tamil": "\u0b89\u0b99\u0bcd\u0b95 \u0baa\u0bc6\u0baf\u0bb0\u0bcd \u0b8e\u0ba9\u0bcd\u0ba9?", "example_romanized": "unga peyar enna?"}]'::jsonb,
            'A1',
            true
        ), (
            gen_random_uuid(),
            'ca75e761-0d7f-56c1-a372-eec78d50ef98',
            4,
            'Polite vs. Casual',
            'மரியாதை மற்றும் இயல்பான பேச்சு',
            'Be more polite with elders or people you don''t know well.',
            '[{"example_english": "Use ''unga'' (\u0b89\u0b99\u0bcd\u0b95) instead of ''un'' (\u0b89\u0ba9\u0bcd) to be more polite. Instead of sollu(\u0b9a\u0bca\u0bb2\u0bcd\u0bb2\u0bc1) use sollunga (\u0b9a\u0bca\u0bb2\u0bcd\u0bb2\u0bc1\u0b99\u0bcd\u0b95)", "example_tamil": " ''\u0b89\u0ba9\u0bcd'' \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bb1\u0bcd\u0b95\u0bc1 \u0baa\u0ba4\u0bbf\u0bb2\u0bbe\u0b95 ''\u0b89\u0b99\u0bcd\u0b95'' \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd. ", "example_romanized": "Use ''unga'' instead of ''un''"}, {"example_english": "Use ''neenga'' (\u0ba8\u0bc0\u0b99\u0bcd\u0b95) instead of ''nee'' (\u0ba8\u0bc0) to be polite when asking a question.", "example_tamil": "''\u0ba8\u0bc0'' \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bb1\u0bcd\u0b95\u0bc1 \u0baa\u0ba4\u0bbf\u0bb2\u0bbe\u0b95 ''\u0ba8\u0bc0\u0b99\u0bcd\u0b95'' \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd.", "example_romanized": "Use ''neenga'' instead of ''nee''"}]'::jsonb,
            'A1',
            true
        ), (
            gen_random_uuid(),
            'ca75e761-0d7f-56c1-a372-eec78d50ef98',
            5,
            'Basic Responses',
            'அடிப்படை பதில்கள்',
            'Say ''சரி'' (sari) to mean ''okay'' or ''yes''.',
            '[{"example_english": "Okay", "example_tamil": "\u0b9a\u0bb0\u0bbf", "example_romanized": "sari"}, {"example_english": "Thank you", "example_tamil": "\u0ba8\u0ba9\u0bcd\u0bb1\u0bbf", "example_romanized": "nandri"}]'::jsonb,
            'A1',
            true
        );
