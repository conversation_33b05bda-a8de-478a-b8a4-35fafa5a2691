import json

def main():
    # Load lesson data
    with open('tamil_a1_1_basic_greetings.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    vocab = data.get('vocabulary', [])
    lesson_id = '707c84e5-daa8-4c9e-b7ba-ff4b78686052'
    
    print("-- Deploy Tamil A1.1 Vocabulary")
    
    for i, item in enumerate(vocab):
        # Clean strings and escape quotes
        word_eng = item.get('word_english', '').replace("'", "''")
        word_tam = item.get('word_tamil', '').replace("'", "''") 
        word_rom = item.get('word_romanized', '').replace("'", "''")
        example_eng = item.get('example_sentence_english', '').replace("'", "''")
        example_tam = item.get('example_sentence_tamil', '').replace("'", "''")
        example_rom = item.get('example_sentence_romanized', '').replace("'", "''")
        
        sql = f"""INSERT INTO lesson_vocabulary (
    id, lesson_id, word_order, word_english, word_target_language, 
    word_romanized, example_sentence_english, example_sentence_target_language, 
    example_sentence_romanized, difficulty_level
) VALUES (
    gen_random_uuid(), '{lesson_id}', {item.get('word_order', i+1)}, 
    '{word_eng}', '{word_tam}', '{word_rom}', 
    '{example_eng}', '{example_tam}', '{example_rom}', 1
);"""
        print(sql)
        print()

if __name__ == "__main__":
    main() 