#!/usr/bin/env python3
"""
Deploy Tamil A1.1 Basic Greetings lesson to NIRA Supabase database
"""

import json
import sys

def load_lesson_data():
    """Load the lesson data from JSON"""
    try:
        with open('tamil_a1_1_basic_greetings.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ Error: tamil_a1_1_basic_greetings.json not found")
        sys.exit(1)

def create_vocabulary_sql(lesson_id: str, vocab_items: list) -> str:
    """Generate SQL to insert vocabulary items"""
    
    values = []
    for item in vocab_items:
        # Clean and escape strings
        word_eng = item.get('word_english', '').replace("'", "''")
        word_tam = item.get('word_tamil', '').replace("'", "''")
        word_rom = item.get('word_romanized', '').replace("'", "''")
        definition = item.get('definition_english', '').replace("'", "''")
        usage = item.get('usage_example_english', '').replace("'", "''")
        audio_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/lessons/{lesson_id}/audio/vocab_{item.get('word_order', 1)}_tamil.mp3"
        
        values.append(f"""(
            gen_random_uuid(),
            '{lesson_id}',
            {item.get('word_order', 1)},
            '{word_eng}',
            '{word_tam}',
            '{word_rom}',
            '{definition}',
            '{usage}',
            '{audio_url}',
            'beginner',
            true
        )""")
    
    sql = f"""
INSERT INTO vocabulary_items (
    id, lesson_id, sequence_order, word_english, word_target_language, 
    word_romanized, definition_english, example_sentence_english,
    audio_url, difficulty_level, is_active
) VALUES {', '.join(values)};
"""
    return sql

def create_conversation_sql(lesson_id: str, conversations: list) -> str:
    """Generate SQL to insert conversations"""
    
    values = []
    for i, conv in enumerate(conversations):
        title_eng = conv.get('title_english', '').replace("'", "''")
        title_tam = conv.get('title_tamil', '').replace("'", "''")
        context = conv.get('context_english', '').replace("'", "''")
        
        # Convert dialogue to JSON string
        dialogue_json = json.dumps(conv.get('dialogue', []))
        dialogue_json = dialogue_json.replace("'", "''")
        
        audio_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/lessons/{lesson_id}/audio/conversation_{i+1}_tamil.mp3"
        
        values.append(f"""(
            gen_random_uuid(),
            '{lesson_id}',
            {i + 1},
            '{title_eng}',
            '{title_tam}',
            '{context}',
            '{dialogue_json}'::jsonb,
            '{audio_url}',
            true
        )""")
    
    sql = f"""
INSERT INTO conversations (
    id, lesson_id, sequence_order, title_english, title_target_language,
    context_description, dialogue_content, audio_url, is_active
) VALUES {', '.join(values)};
"""
    return sql

def create_grammar_sql(lesson_id: str, grammar_points: list) -> str:
    """Generate SQL to insert grammar points"""
    
    values = []
    for i, point in enumerate(grammar_points):
        concept_eng = point.get('concept_english', '').replace("'", "''")
        concept_tam = point.get('concept_tamil', '').replace("'", "''")
        rule_eng = point.get('rule_explanation_english', '').replace("'", "''")
        examples = json.dumps(point.get('examples', []))
        examples = examples.replace("'", "''")
        
        values.append(f"""(
            gen_random_uuid(),
            '{lesson_id}',
            {i + 1},
            '{concept_eng}',
            '{concept_tam}',
            '{rule_eng}',
            '{examples}'::jsonb,
            'A1',
            true
        )""")
    
    sql = f"""
INSERT INTO grammar_points (
    id, lesson_id, sequence_order, concept_english, concept_target_language,
    explanation_english, examples, difficulty_level, is_active
) VALUES {', '.join(values)};
"""
    return sql

def create_exercise_sql(lesson_id: str, exercises: list) -> str:
    """Generate SQL to insert exercises"""
    
    values = []
    for i, exercise in enumerate(exercises):
        question = exercise.get('question_english', '').replace("'", "''")
        ex_type = exercise.get('exercise_type', 'multiple_choice')
        options = json.dumps(exercise.get('options', []))
        options = options.replace("'", "''")
        correct_answer = exercise.get('correct_answer', '').replace("'", "''")
        explanation = exercise.get('explanation_english', '').replace("'", "''")
        
        values.append(f"""(
            gen_random_uuid(),
            '{lesson_id}',
            {i + 1},
            '{ex_type}',
            '{question}',
            '{options}'::jsonb,
            '{correct_answer}',
            '{explanation}',
            {exercise.get('points', 10)},
            true
        )""")
    
    sql = f"""
INSERT INTO exercises (
    id, lesson_id, sequence_order, exercise_type, question_text,
    answer_options, correct_answer, explanation, points_value, is_active
) VALUES {', '.join(values)};
"""
    return sql

def main():
    print("🚀 Deploying Tamil A1.1 - Basic Greetings to NIRA Database")
    print("=" * 60)
    
    # Load lesson data
    lesson_data = load_lesson_data()
    
    # Get lesson ID from database (we know it was just created)
    lesson_id = "ca75e761-0d7f-56c1-a372-eec78d50ef98"  # From previous query
    
    print(f"📝 Target Lesson ID: {lesson_id}")
    print(f"📊 Content Summary:")
    print(f"   • Vocabulary: {len(lesson_data.get('vocabulary', []))} items")
    print(f"   • Conversations: {len(lesson_data.get('conversations', []))} items") 
    print(f"   • Grammar: {len(lesson_data.get('grammar', []))} points")
    print(f"   • Exercises: {len(lesson_data.get('exercises', []))} items")
    print()
    
    # Generate SQL statements
    vocab_sql = create_vocabulary_sql(lesson_id, lesson_data.get('vocabulary', []))
    conv_sql = create_conversation_sql(lesson_id, lesson_data.get('conversations', []))
    grammar_sql = create_grammar_sql(lesson_id, lesson_data.get('grammar', []))
    exercise_sql = create_exercise_sql(lesson_id, lesson_data.get('exercises', []))
    
    # Save SQL files for manual execution via MCP
    with open('deploy_vocabulary.sql', 'w', encoding='utf-8') as f:
        f.write(vocab_sql)
    
    with open('deploy_conversations.sql', 'w', encoding='utf-8') as f:
        f.write(conv_sql)
        
    with open('deploy_grammar.sql', 'w', encoding='utf-8') as f:
        f.write(grammar_sql)
        
    with open('deploy_exercises.sql', 'w', encoding='utf-8') as f:
        f.write(exercise_sql)
    
    print("✅ SQL deployment files created:")
    print("   • deploy_vocabulary.sql")
    print("   • deploy_conversations.sql") 
    print("   • deploy_grammar.sql")
    print("   • deploy_exercises.sql")
    print()
    print("🎯 Ready for MCP Supabase execution!")

if __name__ == "__main__":
    main() 