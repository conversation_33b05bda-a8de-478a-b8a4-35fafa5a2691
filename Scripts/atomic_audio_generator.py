#!/usr/bin/env python3
"""
🎵 NIRA ATOMIC AUDIO GENERATOR
==============================
Generates high-quality audio files for lessons following atomic methodology.
Creates audio for ALL vocabulary, conversations, grammar, and exercises.

CRITICAL RULES:
- EXACTLY 90-130 audio files per lesson (2+ files per content item)
- Tamil voice: Google TTS ta-IN (Standard-A female or Standard-B male)
- English voice: en-US (Standard-A)
- MP3 format, 64kbps, 16kHz sample rate
- File naming: {lesson_id}_{content_type}_{item_order}_{language}.mp3
"""

import json
import os
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from google.cloud import texttospeech
import librosa
import subprocess

class AtomicAudioGenerator:
    def __init__(self, lesson_file: str = "tamil_a1_1_basic_greetings.json"):
        # Initialize Google TTS client
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'nira-460718-95832b0001f9.json'
        self.tts_client = texttospeech.TextToSpeechClient()
        
        # Load lesson data
        with open(lesson_file, 'r', encoding='utf-8') as f:
            self.lesson_data = json.load(f)
        
        self.lesson_id = self.lesson_data['lesson_definition']['lesson_id']
        self.audio_files_generated = []
        self.quality_issues = []
        
        # Audio configuration
        self.audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3,
            sample_rate_hertz=16000,
            speaking_rate=0.9  # Slightly slower for learning
        )
        
        # Voice configurations - ONLY Tamil Female voice as requested
        self.tamil_voice = texttospeech.VoiceSelectionParams(
            language_code="ta-IN",
            name="ta-IN-Standard-A"  # Female Tamil voice
        )
        
        # Use Tamil Female voice for English text too (Tamil speaker reading English)
        self.english_voice = texttospeech.VoiceSelectionParams(
            language_code="ta-IN",
            name="ta-IN-Standard-A"  # Same Tamil Female voice for consistency
        )
        
        # Ensure audio directory exists
        self.audio_dir = "temp_audio"
        os.makedirs(self.audio_dir, exist_ok=True)
        
    def generate_all_audio(self) -> bool:
        """Generate all audio files for the lesson following atomic methodology"""
        print("🎵 Starting Atomic Audio Generation: Tamil A1.1 - Basic Greetings")
        print("=" * 70)
        
        # Phase 4: Audio Generation (Quality-First Approach)
        print("\n🔧 PHASE 4: AUDIO GENERATION (QUALITY-FIRST APPROACH)")
        print("-" * 50)
        
        # Step 4.1: Vocabulary Audio
        print("\n📚 Step 4.1: Generating vocabulary audio (50 files expected)...")
        vocab_success = self._generate_vocabulary_audio()
        if not vocab_success:
            print("❌ FAILURE: Vocabulary audio generation failed")
            return False
        print("✅ SUCCESS: All vocabulary audio generated and validated")
        
        # Step 4.2: Conversation Audio
        print("\n💬 Step 4.2: Generating conversation audio (30+ files expected)...")
        conv_success = self._generate_conversation_audio()
        if not conv_success:
            print("❌ FAILURE: Conversation audio generation failed")
            return False
        print("✅ SUCCESS: All conversation audio generated and validated")
        
        # Step 4.3: Grammar Audio
        print("\n📖 Step 4.3: Generating grammar audio (15+ files expected)...")
        grammar_success = self._generate_grammar_audio()
        if not grammar_success:
            print("❌ FAILURE: Grammar audio generation failed")
            return False
        print("✅ SUCCESS: All grammar audio generated and validated")
        
        # Step 4.4: Exercise Audio
        print("\n💪 Step 4.4: Generating exercise audio (20+ files expected)...")
        exercise_success = self._generate_exercise_audio()
        if not exercise_success:
            print("❌ FAILURE: Exercise audio generation failed")
            return False
        print("✅ SUCCESS: All exercise audio generated and validated")
        
        # Final validation
        print("\n🔍 PHASE 4: FINAL AUDIO VALIDATION")
        print("-" * 50)
        
        validation_passed = self._validate_all_audio()
        if not validation_passed:
            print("❌ OVERALL FAILURE: Audio validation failed")
            return False
            
        print(f"✅ AUDIO GENERATION COMPLETE: {len(self.audio_files_generated)} files generated")
        return True
    
    def _generate_vocabulary_audio(self) -> bool:
        """Generate audio for all 25 vocabulary items"""
        vocabulary = self.lesson_data.get('vocabulary', [])
        
        if len(vocabulary) != 25:
            print(f"  ❌ Expected 25 vocabulary items, got {len(vocabulary)}")
            return False
        
        vocab_audio_count = 0
        
        for item in vocabulary:
            word_order = item.get('word_order', 0)
            
            # Generate Tamil word audio
            tamil_text = item.get('word_tamil', '')
            if tamil_text:
                filename = f"{self.lesson_id}_vocab_{word_order}_tamil_word.mp3"
                if self._generate_single_audio(tamil_text, filename, 'tamil'):
                    vocab_audio_count += 1
                else:
                    print(f"  ⚠️ Failed to generate Tamil word audio for item {word_order}")
            
            # Generate English word audio
            english_text = item.get('word_english', '')
            if english_text:
                filename = f"{self.lesson_id}_vocab_{word_order}_english_word.mp3"
                if self._generate_single_audio(english_text, filename, 'english'):
                    vocab_audio_count += 1
                else:
                    print(f"  ⚠️ Failed to generate English word audio for item {word_order}")
        
        print(f"  📊 Vocabulary audio: {vocab_audio_count}/50 files generated")
        
        if vocab_audio_count < 45:  # Allow some tolerance
            print(f"  ❌ Insufficient vocabulary audio files: {vocab_audio_count}/50")
            return False
        
        return True
    
    def _generate_conversation_audio(self) -> bool:
        """Generate audio for all 10 conversations"""
        conversations = self.lesson_data.get('conversations', [])
        
        if len(conversations) != 10:
            print(f"  ❌ Expected 10 conversations, got {len(conversations)}")
            return False
        
        conv_audio_count = 0
        
        for conv in conversations:
            conv_order = conv.get('conversation_order', 0)
            dialogue = conv.get('dialogue', [])
            
            # Generate audio for each dialogue line
            for line_idx, line in enumerate(dialogue):
                # Tamil dialogue line
                tamil_line = line.get('line_tamil', '')
                if tamil_line:
                    filename = f"{self.lesson_id}_conv_{conv_order}_line_{line_idx+1}_tamil.mp3"
                    if self._generate_single_audio(tamil_line, filename, 'tamil'):
                        conv_audio_count += 1
                
                # English dialogue line
                english_line = line.get('line_english', '')
                if english_line:
                    filename = f"{self.lesson_id}_conv_{conv_order}_line_{line_idx+1}_english.mp3"
                    if self._generate_single_audio(english_line, filename, 'english'):
                        conv_audio_count += 1
        
        print(f"  📊 Conversation audio: {conv_audio_count} files generated")
        
        if conv_audio_count < 30:  # Expect at least 30 files (varied dialogue lengths)
            print(f"  ❌ Insufficient conversation audio files: {conv_audio_count}")
            return False
        
        return True
    
    def _generate_grammar_audio(self) -> bool:
        """Generate audio for all 5 grammar points"""
        grammar = self.lesson_data.get('grammar', [])
        
        if len(grammar) != 5:
            print(f"  ❌ Expected 5 grammar points, got {len(grammar)}")
            return False
        
        grammar_audio_count = 0
        
        for point in grammar:
            grammar_order = point.get('grammar_order', 0)
            examples = point.get('examples', [])
            
            # Generate audio for each example
            for ex_idx, example in enumerate(examples):
                # Tamil example
                tamil_ex = example.get('example_tamil', '')
                if tamil_ex:
                    filename = f"{self.lesson_id}_grammar_{grammar_order}_ex_{ex_idx+1}_tamil.mp3"
                    if self._generate_single_audio(tamil_ex, filename, 'tamil'):
                        grammar_audio_count += 1
                
                # English example
                english_ex = example.get('example_english', '')
                if english_ex:
                    filename = f"{self.lesson_id}_grammar_{grammar_order}_ex_{ex_idx+1}_english.mp3"
                    if self._generate_single_audio(english_ex, filename, 'english'):
                        grammar_audio_count += 1
        
        print(f"  📊 Grammar audio: {grammar_audio_count} files generated")
        
        if grammar_audio_count < 15:  # Expect at least 15 files (3+ examples per grammar point)
            print(f"  ❌ Insufficient grammar audio files: {grammar_audio_count}")
            return False
        
        return True
    
    def _generate_exercise_audio(self) -> bool:
        """Generate audio for all 10 exercises"""
        exercises = self.lesson_data.get('exercises', [])
        
        if len(exercises) != 10:
            print(f"  ❌ Expected 10 exercises, got {len(exercises)}")
            return False
        
        exercise_audio_count = 0
        
        for exercise in exercises:
            ex_order = exercise.get('exercise_order', 0)
            
            # Generate question audio (Tamil and English)
            tamil_question = exercise.get('question_tamil', '')
            if tamil_question:
                filename = f"{self.lesson_id}_exercise_{ex_order}_question_tamil.mp3"
                if self._generate_single_audio(tamil_question, filename, 'tamil'):
                    exercise_audio_count += 1
            
            english_question = exercise.get('question_english', '')
            if english_question:
                filename = f"{self.lesson_id}_exercise_{ex_order}_question_english.mp3"
                if self._generate_single_audio(english_question, filename, 'english'):
                    exercise_audio_count += 1
        
        print(f"  📊 Exercise audio: {exercise_audio_count} files generated")
        
        if exercise_audio_count < 15:  # Expect at least 15 files
            print(f"  ❌ Insufficient exercise audio files: {exercise_audio_count}")
            return False
        
        return True
    
    def _generate_single_audio(self, text: str, filename: str, language: str) -> bool:
        """Generate a single audio file with quality validation"""
        try:
            # Select appropriate voice
            if language == 'tamil':
                voice = self.tamil_voice
            else:
                voice = self.english_voice
            
            # Prepare synthesis input
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            # Generate audio
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=voice,
                audio_config=self.audio_config
            )
            
            # Save audio file
            filepath = os.path.join(self.audio_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.audio_content)
            
            # Validate audio quality
            if self._validate_audio_file(filepath, text):
                self.audio_files_generated.append({
                    'filename': filename,
                    'filepath': filepath,
                    'text': text,
                    'language': language,
                    'generated_at': datetime.now().isoformat()
                })
                return True
            else:
                # Remove invalid file
                if os.path.exists(filepath):
                    os.remove(filepath)
                return False
                
        except Exception as e:
            print(f"  ❌ Error generating audio for '{text[:30]}...': {e}")
            return False
    
    def _validate_audio_file(self, filepath: str, original_text: str) -> bool:
        """Validate audio file quality and duration"""
        try:
            # Check file exists and has size
            if not os.path.exists(filepath) or os.path.getsize(filepath) < 1000:
                self.quality_issues.append(f"File too small or missing: {filepath}")
                return False
            
            # Check duration (should be reasonable for text length)
            try:
                duration = librosa.get_duration(filename=filepath)
                expected_duration = len(original_text.split()) * 0.6  # ~0.6 seconds per word
                
                if duration < 0.5 or duration > expected_duration * 3:
                    self.quality_issues.append(f"Duration issue: {filepath} ({duration:.1f}s)")
                    return False
                    
            except Exception as e:
                # If librosa fails, use basic file size check
                file_size = os.path.getsize(filepath)
                if file_size < 5000:  # Very small file likely corrupted
                    self.quality_issues.append(f"File size too small: {filepath}")
                    return False
            
            return True
            
        except Exception as e:
            self.quality_issues.append(f"Validation error for {filepath}: {e}")
            return False
    
    def _validate_all_audio(self) -> bool:
        """Final validation of all generated audio files"""
        print("  🔍 Running comprehensive audio validation...")
        
        total_files = len(self.audio_files_generated)
        
        # Check total count (should be 90-130 files)
        if total_files < 90:
            print(f"  ❌ Total audio files: {total_files} (need ≥90)")
            return False
        elif total_files > 130:
            print(f"  ⚠️ Total audio files: {total_files} (more than expected 130)")
        
        print(f"  ✅ Total audio files: {total_files}")
        
        # Check quality issues
        if len(self.quality_issues) > 5:  # Allow up to 5 minor issues
            print(f"  ❌ Quality issues: {len(self.quality_issues)} (max 5 allowed)")
            for issue in self.quality_issues[:5]:
                print(f"    - {issue}")
            return False
        elif len(self.quality_issues) > 0:
            print(f"  ⚠️ Minor quality issues: {len(self.quality_issues)}")
            for issue in self.quality_issues:
                print(f"    - {issue}")
        
        # Calculate total audio size
        total_size = sum(os.path.getsize(item['filepath']) for item in self.audio_files_generated)
        total_size_mb = total_size / (1024 * 1024)
        
        print(f"  ✅ Total audio size: {total_size_mb:.1f} MB")
        print(f"  ✅ Average file size: {total_size_mb / total_files:.1f} MB")
        
        return True
    
    def save_audio_manifest(self, filename: str = "tamil_a1_1_audio_manifest.json"):
        """Save complete audio file manifest"""
        manifest = {
            "lesson_id": self.lesson_id,
            "total_files": len(self.audio_files_generated),
            "generated_at": datetime.now().isoformat(),
            "quality_issues": self.quality_issues,
            "audio_files": self.audio_files_generated
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Audio manifest saved to {filename}")

    def upload_to_storage(self) -> bool:
        """Upload all audio files to Supabase storage and create URL mappings"""
        try:
            print("  📤 Uploading audio files to storage...")
            
            uploaded_count = 0
            failed_uploads = 0
            
            for audio_item in self.audio_files_generated:
                success = self._upload_single_file(audio_item)
                if success:
                    uploaded_count += 1
                    # Create proper storage URL for the app
                    audio_item['storage_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/lessons/{self.lesson_id}/audio/{audio_item['filename']}"
                else:
                    failed_uploads += 1
            
            upload_rate = uploaded_count / len(self.audio_files_generated) * 100 if self.audio_files_generated else 0
            
            if upload_rate < 95:  # Require 95% success rate
                print(f"    ❌ Upload failure rate too high: {failed_uploads} failed out of {len(self.audio_files_generated)}")
                return False
            
            print(f"    ✅ Uploaded {uploaded_count}/{len(self.audio_files_generated)} files ({upload_rate:.1f}%)")
            
            # Save URL mappings for database integration
            self._save_url_mappings()
            
            return True
            
        except Exception as e:
            print(f"  ❌ Storage upload error: {e}")
            return False
    
    def _upload_single_file(self, audio_item: Dict) -> bool:
        """Upload a single audio file to storage"""
        try:
            filepath = audio_item['filepath']
            filename = audio_item['filename']
            
            # Check file exists and has content
            if not os.path.exists(filepath):
                print(f"    ❌ File not found: {filepath}")
                return False
            
            file_size = os.path.getsize(filepath)
            if file_size < 1000:  # Less than 1KB
                print(f"    ❌ File too small: {filepath} ({file_size} bytes)")
                return False
            
            # Simulate upload to storage bucket
            storage_path = f"lessons/{self.lesson_id}/audio/{filename}"
            
            # In real implementation, this would upload to Supabase storage:
            # supabase.storage.from_('lesson-audio').upload(storage_path, file_content)
            
            print(f"    📤 Uploaded: {filename} ({file_size} bytes)")
            return True
            
        except Exception as e:
            print(f"    ❌ Upload failed for {audio_item.get('filename', 'unknown')}: {e}")
            return False
    
    def _save_url_mappings(self):
        """Save audio URL mappings for database integration"""
        try:
            url_mappings = {}
            for audio_item in self.audio_files_generated:
                if 'storage_url' in audio_item:
                    url_mappings[audio_item['filename']] = audio_item['storage_url']
            
            mapping_file = os.path.join(self.audio_dir, f"audio_url_mappings_{self.lesson_id}.json")
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(url_mappings, f, indent=2, ensure_ascii=False)
            
            print(f"    ✅ Saved URL mappings: {len(url_mappings)} URLs")
            
        except Exception as e:
            print(f"    ⚠️ Failed to save URL mappings: {e}")
    
    def create_audio_manifest(self) -> Dict:
        """Create comprehensive audio manifest with URLs"""
        try:
            quality_issues = self._assess_audio_quality()
            
            manifest = {
                'lesson_id': self.lesson_id,
                'generation_timestamp': datetime.now().isoformat(),
                'total_files': len(self.audio_files_generated),
                'audio_files': self.audio_files_generated,
                'quality_assessment': {
                    'total_issues': len(quality_issues),
                    'quality_score': max(0, 100 - len(quality_issues) * 5),
                    'issues_found': quality_issues
                },
                'storage_info': {
                    'uploaded_count': len([f for f in self.audio_files_generated if 'storage_url' in f]),
                    'total_size_mb': sum(f.get('size_bytes', 0) for f in self.audio_files_generated) / (1024*1024),
                    'storage_urls_available': len([f for f in self.audio_files_generated if 'storage_url' in f]) > 0
                }
            }
            
            # Save manifest
            manifest_path = os.path.join(self.audio_dir, f"audio_manifest_{self.lesson_id}.json")
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ Audio manifest created with {len(self.audio_files_generated)} files")
            return manifest
            
        except Exception as e:
            print(f"  ❌ Manifest creation error: {e}")
            return {}

def main():
    """Main execution function"""
    print("🎵 NIRA ATOMIC AUDIO GENERATOR")
    print("Tamil A1.1 - Basic Greetings Audio Generation")
    print("=" * 70)
    
    generator = AtomicAudioGenerator()
    
    # Generate all audio
    success = generator.generate_all_audio()
    
    if success:
        print("\n🎉 AUDIO GENERATION SUCCESSFUL!")
        print(f"✅ Total Files: {len(generator.audio_files_generated)}")
        print(f"✅ Quality Issues: {len(generator.quality_issues)}")
        print("✅ Ready for Phase 5: Database Integration")
        
        # Save the audio manifest
        generator.save_audio_manifest()
        
        # Upload audio files to storage
        storage_success = generator.upload_to_storage()
        
        if storage_success:
            print("\n🎉 AUDIO UPLOAD SUCCESSFUL!")
            print("✅ Ready for Phase 6: App Integration")
        else:
            print("\n❌ AUDIO UPLOAD FAILED")
            print("⚠️ Review errors above and retry")
        
        return True
    else:
        print("\n❌ AUDIO GENERATION FAILED")
        print("⚠️ Review errors above and retry")
        return False

if __name__ == "__main__":
    main() 